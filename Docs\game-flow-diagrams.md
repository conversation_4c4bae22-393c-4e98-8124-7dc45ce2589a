# 狼人杀游戏流程图汇总

**版本：** v0.6.0  
**创建日期：** 2025-01-16  
**汇总范围：** 所有游戏相关的流程图和时序图

## 概述

本文档汇总了狼人杀插件项目中的所有流程图，包括游戏状态转换、角色行动时序、完整生命周期等，为开发者提供可视化的游戏逻辑理解。

## 1. 核心流程图索引

### 1.1 游戏状态流程图
**文档位置：** [game-state-flow.md](./game-state-flow.md)  
**基于代码：** `model/core/StateMachine.js`  
**主要内容：**
- 6种游戏状态的转换关系
- 状态转换条件和验证机制
- 特殊流程处理（首日警长选举、警长移交）

### 1.2 角色行动时序图
**文档位置：** [role-action-sequence.md](./role-action-sequence.md)  
**基于代码：** `model/action/NightState.js`  
**主要内容：**
- 夜晚阶段角色行动顺序
- 角色间交互关系
- 行动队列管理机制

### 1.3 完整游戏生命周期流程图
**文档位置：** [game-lifecycle-flow.md](./game-lifecycle-flow.md)  
**基于代码：** `apps/GameStart.js`, `model/services/GameLobby.js`  
**主要内容：**
- 从游戏创建到结束的完整流程
- 玩家加入和退出处理
- 资源管理和清理机制

## 2. 快速参考图表

### 2.1 游戏状态转换概览

```mermaid
stateDiagram-v2
    [*] --> NightState : 游戏开始
    NightState --> DayState : 夜晚结束
    DayState --> VoteState : 讨论结束
    DayState --> SheriffElectState : 首日警长选举
    VoteState --> LastWordsState : 有人被驱逐
    VoteState --> NightState : 平票无人驱逐
    LastWordsState --> NightState : 普通死亡
    LastWordsState --> SheriffTransferState : 警长死亡
    SheriffElectState --> DayState : 选举结束
    SheriffTransferState --> NightState : 移交完成
    
    NightState --> [*] : 游戏结束
    DayState --> [*] : 游戏结束
    VoteState --> [*] : 游戏结束
    LastWordsState --> [*] : 游戏结束
```

### 2.2 角色行动顺序概览

```mermaid
sequenceDiagram
    participant Night as 夜晚阶段
    participant Guard as 守卫
    participant Prophet as 预言家
    participant Wolf as 狼人
    participant Witch as 女巫
    participant Day as 白天阶段

    Night->>Guard: 1. 守卫行动
    Guard->>Night: 选择守护目标
    Night->>Prophet: 2. 预言家行动
    Prophet->>Night: 查验身份
    Night->>Wolf: 3. 狼人行动
    Wolf->>Night: 选择击杀目标
    Night->>Witch: 4. 女巫行动
    Witch->>Night: 使用药水
    Night->>Day: 夜晚结束，进入白天
```

### 2.3 游戏生命周期概览

```mermaid
flowchart TD
    A[用户发起创建游戏] --> B[创建GameLobby]
    B --> C[玩家加入大厅]
    C --> D{人数是否足够?}
    D -->|否| C
    D -->|是| E[创建Game实例]
    E --> F[角色分配]
    F --> G[游戏开始]
    G --> H[状态循环]
    H --> I{胜利条件?}
    I -->|否| H
    I -->|是| J[游戏结束]
    J --> K[资源清理]
    K --> L[统计记录]
```

## 3. 详细流程图说明

### 3.1 状态机核心特性
- **严格验证**：每次状态转换都经过条件验证
- **历史记录**：完整的状态转换历史追踪
- **错误处理**：非法转换的错误处理机制
- **事件驱动**：状态变更触发相应事件

### 3.2 角色系统特性
- **有序执行**：严格按照actionQueue顺序执行
- **异步处理**：支持角色行动的异步处理
- **状态同步**：行动结果实时同步到游戏状态
- **错误恢复**：行动失败的恢复机制

### 3.3 生命周期管理特性
- **资源管理**：完善的内存和资源清理
- **状态持久化**：关键状态的持久化存储
- **并发控制**：多玩家并发操作的控制
- **异常处理**：游戏异常的处理和恢复

## 4. 流程图使用指南

### 4.1 开发者使用建议
1. **新功能开发**：先查看相关流程图理解现有逻辑
2. **问题排查**：通过流程图定位问题可能出现的环节
3. **代码审查**：对照流程图验证代码实现的正确性
4. **文档更新**：代码变更后及时更新对应流程图

### 4.2 测试用例设计
- 基于状态转换图设计状态转换测试
- 根据角色时序图设计角色行动测试
- 参考生命周期图设计端到端测试

### 4.3 性能优化参考
- 识别流程中的性能瓶颈点
- 优化关键路径的执行效率
- 减少不必要的状态转换

## 5. 流程图维护规范

### 5.1 更新触发条件
- 游戏状态逻辑变更
- 角色系统功能调整
- 生命周期管理优化
- 新增特殊流程处理

### 5.2 质量检查要点
- 流程图与代码实现一致性
- Mermaid语法正确性
- 图表可读性和美观性
- 文档链接有效性

### 5.3 版本管理
- 流程图版本与项目版本同步
- 重大变更需要版本说明
- 保留历史版本的变更记录

## 总结

本汇总文档提供了狼人杀插件项目所有流程图的统一入口，通过可视化的方式帮助开发者理解复杂的游戏逻辑。建议开发者在进行相关开发工作前，先通过这些流程图建立对系统的整体认知。

**关键价值：**
- 🎯 **快速理解**：通过图表快速掌握游戏逻辑
- 🔍 **问题定位**：可视化辅助问题排查
- 📋 **开发指导**：为新功能开发提供参考
- 🧪 **测试设计**：为测试用例设计提供依据

所有流程图都基于实际代码分析生成，确保了准确性和实用性。
