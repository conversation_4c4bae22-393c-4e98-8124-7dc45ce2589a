# API参考文档

**版本：** v0.6.0  
**更新日期：** 2025-01-16  
**API状态：** 稳定版本

## 概述

本文档描述了狼人杀插件的核心API接口，包括游戏管理、玩家操作、状态控制等功能模块。

## 核心API

### Game类

游戏核心协调器，负责整体游戏流程控制。

#### 构造函数

```javascript
new Game(options)
```

**参数：**
- `options.config` (Object): 游戏配置
- `options.stateMachine` (StateMachine): 状态机实例
- `options.eventHandler` (EventHandler): 事件处理器
- `options.victoryChecker` (VictoryChecker): 胜利条件检查器

**示例：**
```javascript
const game = new Game({
  config: gameConfig,
  stateMachine: new StateMachine(),
  eventHandler: new GameEventHandler(game, e)
});
```

#### 方法

##### addPlayer(player)

添加玩家到游戏。

**参数：**
- `player` (Player): 玩家对象

**返回值：**
- `boolean`: 是否成功添加

**示例：**
```javascript
const success = game.addPlayer({
  id: 'player1',
  name: 'Alice',
  isAlive: true
});
```

##### hasPlayer(playerId)

检查玩家是否在游戏中。

**参数：**
- `playerId` (string): 玩家ID

**返回值：**
- `boolean`: 玩家是否存在

##### getAlivePlayers(options)

获取存活玩家列表。

**参数：**
- `options.excludeCamp` (string, 可选): 排除的阵营
- `options.roleType` (string, 可选): 角色类型过滤
- `options.showRole` (boolean, 可选): 是否显示角色
- `options.showStatus` (boolean, 可选): 是否显示状态

**返回值：**
- `Array<Player>`: 存活玩家数组

##### async start()

启动游戏。

**返回值：**
- `Promise<boolean>`: 是否成功启动

##### async handleAction(player, action, target)

处理玩家行动。

**参数：**
- `player` (Player|string): 玩家对象或ID
- `action` (string): 行动类型
- `target` (any): 行动目标

**返回值：**
- `Promise<void>`

### PlayerManager类

玩家管理器，负责玩家生命周期管理。

#### 方法

##### addPlayer(player)

添加玩家。

**参数：**
- `player` (Player): 玩家对象

**返回值：**
- `boolean`: 是否成功添加

##### getPlayer(playerId)

获取玩家对象。

**参数：**
- `playerId` (string): 玩家ID

**返回值：**
- `Player|null`: 玩家对象或null

##### async initializePlayerRoles(roleConfig)

初始化玩家角色。

**参数：**
- `roleConfig` (Array<string>): 角色配置数组

**返回值：**
- `Promise<void>`

##### async handlePlayerDeath(player, reason)

处理玩家死亡。

**参数：**
- `player` (Player): 玩家对象
- `reason` (string): 死亡原因

**返回值：**
- `Promise<boolean>`: 是否成功处理

### StateManager类

状态管理器，负责游戏状态转换。

#### 方法

##### initializeState()

初始化游戏状态。

##### async changeState(newState)

改变游戏状态。

**参数：**
- `newState` (GameState): 新状态对象

**返回值：**
- `Promise<void>`

##### getCurrentState()

获取当前状态。

**返回值：**
- `GameState|null`: 当前状态对象

##### getCurrentPhase()

获取当前游戏阶段。

**返回值：**
- `string`: 当前阶段

##### getCurrentTurn()

获取当前回合数。

**返回值：**
- `number`: 回合数

##### isValidAction(player, action)

验证行动是否有效。

**参数：**
- `player` (Player): 玩家对象
- `action` (string): 行动类型

**返回值：**
- `boolean`: 是否有效

### ErrorHandler类

错误处理器，提供统一的错误处理。

#### 方法

##### handle(error, context, e)

处理错误。

**参数：**
- `error` (Error|GameError|string): 错误对象或错误名称
- `context` (Object, 可选): 错误上下文
- `e` (Object, 可选): 通信句柄

**返回值：**
- `Object`: 处理结果
  - `success` (boolean): 是否成功处理
  - `error` (GameError): 标准化错误对象
  - `userMessage` (string): 用户消息
  - `handled` (boolean): 是否已处理

**示例：**
```javascript
const result = errorHandler.handle('INVALID_PLAYER', {
  playerId: 'player1'
}, e);
```

##### createAndHandle(errorName, customMessage, details, context, e)

创建并处理游戏错误。

**参数：**
- `errorName` (string): 错误名称
- `customMessage` (string, 可选): 自定义消息
- `details` (Object, 可选): 错误详情
- `context` (Object, 可选): 上下文
- `e` (Object, 可选): 通信句柄

**返回值：**
- `Object`: 处理结果

### ValidationUtils类

验证工具类，提供统一的验证功能。

#### 静态方法

##### validatePlayer(player, options)

验证玩家对象。

**参数：**
- `player` (Player): 玩家对象
- `options` (Object, 可选): 验证选项
  - `checkAlive` (boolean): 是否检查存活状态
  - `checkRole` (boolean): 是否检查角色
  - `requiredRole` (string): 要求的角色

**返回值：**
- `Object`: 验证结果
  - `isValid` (boolean): 是否有效
  - `error` (GameError, 可选): 错误对象

##### validateGameNumber(number, min, max)

验证游戏号码。

**参数：**
- `number` (any): 要验证的号码
- `min` (number): 最小值
- `max` (number): 最大值

**返回值：**
- `Object`: 验证结果

##### validateAction(player, action, game, options)

验证玩家行动。

**参数：**
- `player` (Player): 玩家对象
- `action` (string): 行动类型
- `game` (Game, 可选): 游戏对象
- `options` (Object, 可选): 验证选项

**返回值：**
- `Object`: 验证结果

## 常量定义

### ROLES (角色)

```javascript
export const ROLES = {
  WOLF: 'WOLF',           // 狼人
  PROPHET: 'PROPHET',     // 预言家
  WITCH: 'WITCH',         // 女巫
  HUNTER: 'HUNTER',       // 猎人
  GUARD: 'GUARD',         // 守卫
  VILLAGER: 'VILLAGER'    // 村民
};
```

### CAMPS (阵营)

```javascript
export const CAMPS = {
  WOLF: 'WOLF',           // 狼人阵营
  GOD: 'GOD',             // 神民阵营
  VILLAGER: 'VILLAGER'    // 村民阵营
};
```

### GAME_PHASES (游戏阶段)

```javascript
export const GAME_PHASES = {
  WAITING: 'waiting',                    // 等待玩家
  STARTING: 'starting',                  // 游戏开始
  SHERIFF_ELECTION: 'sheriff_election',  // 警长竞选
  DAY_DISCUSSION: 'day_discussion',      // 白天讨论
  DAY_VOTING: 'day_voting',             // 白天投票
  NIGHT: 'night',                       // 夜晚
  GAME_END: 'game_end'                  // 游戏结束
};
```

### ACTIONS (行动)

```javascript
export const ACTIONS = {
  VOTE: 'vote',           // 投票
  ABSTAIN: 'abstain',     // 弃权
  SKIP: 'skip',           // 跳过
  REGISTER: 'register',   // 注册
  TRANSFER: 'transfer',   // 转移
  GIVEUP: 'giveup',      // 放弃
  SUPPORT: 'support',     // 支持
  END_SPEECH: 'endSpeech', // 结束发言
  PROTECT: 'protect',     // 保护
  CHECK: 'check',         // 查验
  POISON: 'poison',       // 毒杀
  SAVE: 'save',          // 救人
  KILL: 'kill',          // 杀人
  SUICIDE: 'suicide'      // 自杀
};
```

## 错误代码

### 验证错误 (E11xx)

- `E1100`: 玩家验证失败
- `E1101`: 游戏号码无效
- `E1102`: 角色验证失败

### 状态错误 (E12xx)

- `E1200`: 无效操作
- `E1201`: 游戏状态错误

### 投票错误 (E13xx)

- `E1300`: 投票目标无效
- `E1301`: 目标验证失败

## 事件系统

### 游戏事件

- `gameStart`: 游戏开始
- `gameEnd`: 游戏结束
- `newDay`: 新的一天
- `newTurn`: 新回合
- `stateChanged`: 状态改变

### 玩家事件

- `playerDeath`: 玩家死亡
- `roleNotify`: 角色通知

### 错误事件

- `error`: 错误发生

## 使用示例

### 创建游戏

```javascript
import { Game } from './model/core/Game.js';
import { StateMachine } from './model/core/StateMachine.js';

const game = new Game({
  config: { minPlayers: 6, maxPlayers: 12 },
  stateMachine: new StateMachine()
});

// 添加玩家
game.addPlayer({ id: 'player1', name: 'Alice' });
game.addPlayer({ id: 'player2', name: 'Bob' });

// 启动游戏
await game.start();
```

### 处理玩家行动

```javascript
// 玩家投票
await game.handleAction('player1', 'vote', 'player2');

// 狼人杀人
await game.handleAction('player3', 'kill', 'player1');
```

### 错误处理

```javascript
import { ErrorHandler } from './model/core/ErrorHandler.js';

const errorHandler = new ErrorHandler();

try {
  // 可能出错的操作
  await game.handleAction(invalidPlayer, 'vote', target);
} catch (error) {
  const result = errorHandler.handle(error, { action: 'vote' }, e);
  console.log('错误已处理:', result.userMessage);
}
```

## 版本兼容性

- **v0.6.0**: 当前版本，重构后的稳定API
- **v0.5.x**: 部分兼容，建议升级
- **v0.4.x及以下**: 不兼容，需要重大修改

## 更新日志

### v0.6.0 (2025-01-16)
- 重构核心架构
- 新增PlayerManager和StateManager
- 统一错误处理和验证
- 完善API文档

### v0.5.0
- 服务化架构改进
- 性能优化

### v0.4.0及以前
- 基础功能实现
