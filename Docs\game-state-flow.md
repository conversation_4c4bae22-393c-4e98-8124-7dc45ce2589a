# 狼人杀游戏状态流程图

**版本：** v0.6.0  
**创建日期：** 2025-01-16  
**基于：** model/core/StateMachine.js 状态转换配置

## 概述

本文档基于 `model/core/StateMachine.js` 中的 `StateTransitions` 配置，使用 Mermaid 语法创建详细的游戏状态转换图，展示6种游戏状态间的转换条件和路径。

## 1. 核心状态转换图

### 1.1 主要游戏状态流程

```mermaid
stateDiagram-v2
    [*] --> NightState : 游戏开始
    
    NightState --> DayState : 夜晚结束，进入白天
    
    DayState --> VoteState : 白天讨论结束，进入投票
    DayState --> SheriffElectState : 首日特殊流程，进入警长竞选<br/>(仅第一天: game.turn === 1)
    
    VoteState --> LastWordsState : 投票决定驱逐某人，进入遗言
    VoteState --> NightState : 平票无人驱逐，直接进入夜晚
    
    LastWordsState --> NightState : 遗言结束，进入夜晚<br/>(死者不是警长: !deadPlayer.isSheriff)
    LastWordsState --> SheriffTransferState : 死者是警长，需要移交警徽<br/>(死者是警长: deadPlayer.isSheriff)
    
    SheriffElectState --> DayState : 警长选举结束，继续白天流程
    
    SheriffTransferState --> NightState : 警长移交结束，进入夜晚
    
    NightState --> [*] : 游戏结束
    DayState --> [*] : 游戏结束
    VoteState --> [*] : 游戏结束
    LastWordsState --> [*] : 游戏结束
```

### 1.2 详细状态转换图（包含条件）

```mermaid
stateDiagram-v2
    state "游戏开始" as Start
    state "夜晚阶段" as Night {
        state "守卫行动" as GuardAction
        state "预言家行动" as ProphetAction  
        state "狼人行动" as WolfAction
        state "女巫行动" as WitchAction
        
        GuardAction --> ProphetAction
        ProphetAction --> WolfAction
        WolfAction --> WitchAction
    }
    
    state "白天阶段" as Day {
        state "死亡公布" as DeathAnnounce
        state "自由讨论" as Discussion
        state "发言阶段" as Speaking
        
        DeathAnnounce --> Discussion
        Discussion --> Speaking
    }
    
    state "投票阶段" as Vote {
        state "投票进行" as Voting
        state "票数统计" as CountVotes
        state "平票处理" as TieHandle
        
        Voting --> CountVotes
        CountVotes --> TieHandle
    }
    
    state "遗言阶段" as LastWords {
        state "被驱逐者遗言" as ExiledLastWords
        state "特殊技能触发" as SpecialSkill
        
        ExiledLastWords --> SpecialSkill
    }
    
    state "警长选举" as SheriffElect {
        state "竞选发言" as Campaign
        state "警长投票" as SheriffVote
        state "警长确定" as SheriffConfirm
        
        Campaign --> SheriffVote
        SheriffVote --> SheriffConfirm
    }
    
    state "警长移交" as SheriffTransfer {
        state "移交选择" as TransferChoice
        state "警徽传递" as BadgeTransfer
        
        TransferChoice --> BadgeTransfer
    }
    
    state "游戏结束" as End
    
    Start --> Night
    
    Night --> Day : condition: true<br/>夜晚结束，进入白天
    
    Day --> Vote : condition: true<br/>白天讨论结束，进入投票
    Day --> SheriffElect : condition: game.turn === 1<br/>首日特殊流程，进入警长竞选
    
    Vote --> LastWords : condition: true<br/>投票决定驱逐某人，进入遗言
    Vote --> Night : condition: true<br/>平票无人驱逐，直接进入夜晚
    
    LastWords --> Night : condition: !deadPlayer.isSheriff<br/>遗言结束，进入夜晚
    LastWords --> SheriffTransfer : condition: deadPlayer.isSheriff<br/>死者是警长，需要移交警徽
    
    SheriffElect --> Day : condition: true<br/>警长选举结束，继续白天流程
    
    SheriffTransfer --> Night : condition: true<br/>警长移交结束，进入夜晚
    
    Night --> End : 胜利条件达成
    Day --> End : 胜利条件达成
    Vote --> End : 胜利条件达成
    LastWords --> End : 胜利条件达成
```

## 2. 状态转换条件详解

### 2.1 条件类型分析

| 源状态 | 目标状态 | 转换条件 | 条件类型 | 说明 |
|--------|----------|----------|----------|------|
| NightState | DayState | `true` | 无条件 | 夜晚阶段结束后自动进入白天 |
| DayState | VoteState | `true` | 无条件 | 白天讨论结束后进入投票 |
| DayState | SheriffElectState | `game.turn === 1` | 回合条件 | 仅在第一天进入警长选举 |
| VoteState | LastWordsState | `true` | 无条件 | 有人被投票驱逐时进入遗言 |
| VoteState | NightState | `true` | 无条件 | 平票无人驱逐时直接进入夜晚 |
| LastWordsState | NightState | `!deadPlayer.isSheriff` | 角色条件 | 死者不是警长时直接进入夜晚 |
| LastWordsState | SheriffTransferState | `deadPlayer.isSheriff` | 角色条件 | 死者是警长时需要移交警徽 |
| SheriffElectState | DayState | `true` | 无条件 | 警长选举结束后继续白天流程 |
| SheriffTransferState | NightState | `true` | 无条件 | 警长移交结束后进入夜晚 |

### 2.2 特殊转换逻辑

**首日特殊流程：**
```javascript
// 第一天的特殊处理
if (game.turn === 1) {
  DayState → SheriffElectState → DayState → VoteState
} else {
  DayState → VoteState
}
```

**警长死亡处理：**
```javascript
// 根据死者是否为警长决定流程
if (deadPlayer.isSheriff) {
  LastWordsState → SheriffTransferState → NightState
} else {
  LastWordsState → NightState
}
```

**平票处理：**
```javascript
// 投票结果处理
if (hasPlayerToExile) {
  VoteState → LastWordsState
} else {
  VoteState → NightState  // 平票无人驱逐
}
```

## 3. 状态机验证机制

### 3.1 转换验证流程

```mermaid
flowchart TD
    A[状态转换请求] --> B{源状态是否定义转换?}
    B -->|否| C[拒绝转换: 未定义转换]
    B -->|是| D{目标状态是否允许?}
    D -->|否| E[拒绝转换: 不允许的目标状态]
    D -->|是| F{条件函数是否存在?}
    F -->|否| G[允许转换: 无条件限制]
    F -->|是| H{条件是否满足?}
    H -->|否| I[拒绝转换: 条件不满足]
    H -->|是| J[允许转换: 条件满足]
    
    C --> K[记录错误日志]
    E --> K
    I --> K
    G --> L[执行状态转换]
    J --> L
    L --> M[记录状态历史]
    M --> N[发出状态变更事件]
```

### 3.2 错误处理路径

```mermaid
stateDiagram-v2
    state "正常状态转换" as Normal
    state "转换验证失败" as ValidationFailed
    state "状态转换异常" as TransitionError
    state "错误恢复" as ErrorRecovery
    
    Normal --> ValidationFailed : 验证失败
    Normal --> TransitionError : 执行异常
    
    ValidationFailed --> ErrorRecovery : 记录错误日志
    TransitionError --> ErrorRecovery : 异常处理
    
    ErrorRecovery --> Normal : 恢复到稳定状态
```

## 4. 状态生命周期

### 4.1 状态进入/退出流程

```mermaid
sequenceDiagram
    participant SM as StateMachine
    participant OS as OldState
    participant NS as NewState
    participant Game as Game

    SM->>SM: 验证转换合法性
    SM->>OS: onExit()
    OS->>Game: 清理状态相关数据
    SM->>NS: 设置新状态
    SM->>NS: setContext(game)
    SM->>NS: onEnter()
    NS->>Game: 初始化状态逻辑
    SM->>Game: emit('stateChanged')
```

### 4.2 状态历史记录

每次状态转换都会记录历史信息：
- 状态类型名称
- 转换时间戳  
- 游戏回合数
- 转换上下文

**历史记录结构：**
```javascript
{
  stateType: 'NightState',
  timestamp: new Date(),
  turn: 1,
  context: { /* 转换上下文 */ }
}
```

## 5. 游戏循环模式

### 5.1 标准游戏循环

```mermaid
graph LR
    A[NightState] --> B[DayState]
    B --> C[VoteState]
    C --> D[LastWordsState]
    D --> A
    
    style A fill:#2d3748,stroke:#4a5568,color:#fff
    style B fill:#fbd38d,stroke:#ed8936,color:#000
    style C fill:#fc8181,stroke:#e53e3e,color:#fff
    style D fill:#a78bfa,stroke:#7c3aed,color:#fff
```

### 5.2 首日特殊循环

```mermaid
graph LR
    A[NightState] --> B[DayState]
    B --> C[SheriffElectState]
    C --> D[DayState]
    D --> E[VoteState]
    E --> F[LastWordsState]
    F --> A
    
    style C fill:#68d391,stroke:#38a169,color:#000
```

### 5.3 警长死亡特殊循环

```mermaid
graph LR
    A[VoteState] --> B[LastWordsState]
    B --> C[SheriffTransferState]
    C --> D[NightState]
    
    style C fill:#f6ad55,stroke:#dd6b20,color:#000
```

## 总结

狼人杀游戏的状态机设计具有以下特点：

1. **严格的状态转换规则**：通过 StateTransitions 配置确保游戏流程的正确性
2. **条件化转换**：支持基于游戏状态的动态转换决策
3. **完善的验证机制**：防止非法状态转换
4. **灵活的特殊流程**：支持首日警长选举、警长移交等特殊情况
5. **可追溯的状态历史**：便于调试和问题排查

这种设计确保了游戏流程的可预测性和可维护性，为复杂的狼人杀游戏逻辑提供了坚实的基础。
