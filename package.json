{"name": "werewolf-plugin", "version": "0.6.0", "description": "狼人杀游戏插件 for Miao-Yunzai", "type": "module", "main": "index.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "test": "jest", "test:unit": "cross-env NODE_OPTIONS=\"--experimental-vm-modules\" jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:e2e:run": "node tests/e2e/run-e2e-tests.js", "test:e2e:standard": "jest tests/e2e/game-scenarios/standard-game.test.js", "test:e2e:victory": "jest tests/e2e/victory-conditions/victory-scenarios.test.js", "test:e2e:special": "jest tests/e2e/special-scenarios/special-abilities.test.js", "test:e2e:interactions": "jest tests/e2e/user-interactions/game-creation.test.js", "test:e2e:states": "jest tests/e2e/state-transitions/state-flow.test.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "keywords": ["werewolf", "game", "yunzai", "plugin"], "author": "Your Name", "license": "MIT", "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.50.0", "jest": "^29.7.0", "jest-html-reporters": "^3.1.5"}, "dependencies": {"chokidar": "^3.5.0", "lodash": "^4.17.21", "yaml": "^2.3.0"}}