# 狼人杀插件项目技术文档库

**版本：** v0.6.0  
**最后更新：** 2025-01-16  
**文档体系：** 基于RIPER-5模式的完整技术文档

## 文档导航

### 📋 项目概览
- [项目架构深度分析](./architecture-analysis.md) - 代码级别的架构实现细节
- [代码质量评估报告](./quality-assessment.md) - 代码质量分析和改进建议
- [改进建议和优化方案](./improvement-recommendations.md) - 项目优化路线图

### 🔄 流程图文档
- [游戏状态流程图](./game-state-flow.md) - 基于StateMachine的状态转换图
- [角色行动时序图](./role-action-sequence.md) - 夜晚阶段角色行动顺序
- [完整游戏生命周期流程图](./game-lifecycle-flow.md) - 从创建到结束的全流程
- [游戏流程图汇总](./game-flow-diagrams.md) - 所有流程图的集中展示

### 🧪 测试文档
- [测试设计报告](./test-design-report.md) - 测试基础设施和测试用例设计
- [测试结果分析](./test-results-analysis.md) - 测试执行结果和覆盖率分析
- [端到端测试报告](./e2e-test-report.md) - 完整游戏流程测试结果

### 📊 分析报告
- [性能分析报告](./performance-analysis.md) - 系统性能评估和优化建议
- [安全性分析报告](./security-analysis.md) - 代码安全性评估
- [依赖关系分析](./dependency-analysis.md) - 模块依赖关系详细分析

### 📚 研究文档
- [技术调研报告](./research/) - 深度技术研究成果
- [最佳实践总结](./best-practices.md) - 项目开发最佳实践
- [设计模式应用分析](./design-patterns-analysis.md) - 设计模式在项目中的应用

## 文档使用指南

### 快速开始
1. **新开发者入门**：建议先阅读 [项目架构深度分析](./architecture-analysis.md)
2. **理解游戏流程**：查看 [游戏流程图汇总](./game-flow-diagrams.md)
3. **开发测试**：参考 [测试设计报告](./test-design-report.md)

### 文档维护
- 所有文档遵循Markdown格式
- 流程图使用Mermaid语法
- 版本号与项目主版本保持同步
- 每次重大更新需更新文档时间戳

### 与现有文档体系的关系
本文档库与 `docs/` 目录形成互补：
- `docs/` - 面向用户的使用文档和API参考
- `project_document/` - 面向开发者的技术深度分析

## 文档质量标准

### 内容要求
- ✅ **准确性**：基于实际代码分析，避免假设
- ✅ **完整性**：覆盖项目的所有重要方面
- ✅ **时效性**：与代码版本保持同步
- ✅ **可读性**：结构清晰，便于理解

### 格式规范
- 使用标准Markdown语法
- 代码块指定语言类型
- 图表使用Mermaid语法
- 链接使用相对路径

## 贡献指南

### 文档更新流程
1. 代码变更后及时更新相关文档
2. 新增功能需补充对应的分析文档
3. 重大架构变更需更新架构分析报告
4. 测试用例变更需同步测试文档

### 质量检查清单
- [ ] 文档内容与代码实现一致
- [ ] 所有链接可正常访问
- [ ] Mermaid图表可正常渲染
- [ ] 代码示例可正常运行
- [ ] 版本号和时间戳已更新

## 技术栈说明

### 文档技术栈
- **Markdown**：文档编写格式
- **Mermaid**：流程图和图表
- **Jest**：测试框架文档
- **ESLint**：代码质量分析

### 项目技术栈
- **Node.js**：运行环境
- **ES6+ Modules**：模块系统
- **EventEmitter**：事件驱动架构
- **Jest**：测试框架

## 联系信息

如有文档相关问题或建议，请通过以下方式联系：
- 项目Issue：提交文档改进建议
- 代码Review：文档变更需要代码审查
- 技术讨论：架构和设计相关讨论

---

**注意**：本文档库是项目技术文档的核心，请保持其准确性和时效性。所有重大变更都应该在这里得到体现。
