# 狼人杀插件测试设计报告

**版本：** v0.6.0  
**创建日期：** 2025-01-16  
**测试框架：** Jest + ES6 Modules  
**覆盖范围：** 单元测试、集成测试、端到端测试

## 概述

本报告详细描述了狼人杀插件项目的测试基础设施设计、测试用例规划和测试执行策略。基于CHANGELOG.md中v0.2版本的测试框架设计，重新建立了完整的测试体系。

## 1. 测试基础设施

### 1.1 测试目录结构

```
tests/
├── setup.js                 # 全局测试配置
├── unit/                    # 单元测试
│   ├── README.md
│   ├── core/               # 核心组件测试
│   ├── managers/           # 管理器测试
│   ├── roles/              # 角色系统测试
│   └── utils/              # 工具函数测试
├── integration/            # 集成测试
│   └── README.md
├── e2e/                    # 端到端测试
│   ├── README.md
│   ├── jest.e2e.config.js
│   ├── setup.e2e.js
│   ├── run-e2e-tests.js
│   ├── game-scenarios/     # 游戏场景测试
│   ├── victory-conditions/ # 胜利条件测试
│   ├── special-scenarios/  # 特殊场景测试
│   ├── state-transitions/  # 状态转换测试
│   └── user-interactions/  # 用户交互测试
└── mocks/                  # 模拟对象
    ├── index.js
    ├── MockGame.js
    ├── MockPlayer.js
    └── MockEventHandler.js
```

### 1.2 Jest配置

**主配置文件：** `jest.config.js`
```javascript
export default {
  testEnvironment: 'node',
  transform: {},
  extensionsToTreatAsEsm: ['.js'],
  globals: {
    'ts-jest': {
      useESM: true
    }
  },
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/unit/**/*.test.js',
    '<rootDir>/tests/integration/**/*.test.js'
  ],
  collectCoverageFrom: [
    'model/**/*.js',
    'apps/**/*.js',
    'components/**/*.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

**E2E配置文件：** `tests/e2e/jest.e2e.config.js`
```javascript
export default {
  testEnvironment: 'node',
  transform: {},
  extensionsToTreatAsEsm: ['.js'],
  setupFilesAfterEnv: ['<rootDir>/tests/e2e/setup.e2e.js'],
  testMatch: ['<rootDir>/tests/e2e/**/*.test.js'],
  testTimeout: 30000
}
```

### 1.3 模拟对象工厂

**MockGame.js** - 游戏实例模拟
```javascript
export class MockGame extends EventEmitter {
  constructor(options = {}) {
    super()
    this.players = options.players || []
    this.turn = options.turn || 1
    this.phase = options.phase || 'night'
    this.isGameActive = options.isGameActive || true
  }
  
  // 模拟游戏核心方法
  start() { /* 模拟实现 */ }
  addPlayer(player) { /* 模拟实现 */ }
  removePlayer(playerId) { /* 模拟实现 */ }
}
```

## 2. 测试用例设计

### 2.1 单元测试覆盖

#### 2.1.1 核心组件测试
- **Game.js** - 游戏协调器测试
  - 游戏初始化和配置
  - 玩家管理功能
  - 事件发射和处理
  - 胜利条件检查

- **StateMachine.js** - 状态机测试
  - 状态转换验证
  - 转换条件检查
  - 状态历史记录
  - 错误处理机制

- **VictoryChecker.js** - 胜利检查器测试
  - 各种胜利条件判断
  - 边界情况处理
  - 性能测试

#### 2.1.2 管理器组件测试
- **PlayerManager.js** - 玩家管理器测试
  - 玩家添加/移除
  - 角色分配逻辑
  - 缓存系统验证
  - 死亡处理机制

- **StateManager.js** - 状态管理器测试
  - 状态初始化
  - 状态转换协调
  - 历史记录管理

#### 2.1.3 角色系统测试
- **WolfRole.js** - 狼人角色测试
  - 夜晚击杀行动
  - 团队协作机制
  - 特殊能力验证

- **ProphetRole.js** - 预言家角色测试
  - 查验身份功能
  - 信息获取机制

### 2.2 集成测试设计

#### 2.2.1 组件协作测试
- Game + PlayerManager 集成
- StateMachine + StateManager 集成
- Role + Game 交互测试

#### 2.2.2 服务层集成测试
- GameLobby + GameRegistry 集成
- PlayerStats + Game 数据同步

### 2.3 端到端测试场景

#### 2.3.1 标准游戏流程测试
```javascript
describe('标准6人局游戏流程', () => {
  test('完整游戏从开始到结束', async () => {
    // 1. 创建游戏大厅
    // 2. 6名玩家加入
    // 3. 开始游戏
    // 4. 角色分配验证
    // 5. 夜晚阶段行动
    // 6. 白天阶段讨论投票
    // 7. 胜利条件检查
    // 8. 游戏结束处理
  })
})
```

#### 2.3.2 胜利条件验证测试
- 好人胜利场景测试
- 狼人胜利场景测试
- 第三方胜利场景测试

#### 2.3.3 特殊能力测试
- 警长移交流程测试
- 猎人开枪机制测试
- 女巫药水使用测试

## 3. 测试执行策略

### 3.1 测试分层策略

```mermaid
pyramid
    title 测试金字塔
    
    "E2E Tests" : 20
    "Integration Tests" : 30  
    "Unit Tests" : 50
```

- **单元测试 (50%)**：快速反馈，高覆盖率
- **集成测试 (30%)**：组件协作验证
- **端到端测试 (20%)**：完整流程验证

### 3.2 测试执行命令

```bash
# 运行所有单元测试
npm test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 生成覆盖率报告
npm run test:coverage

# 监视模式运行测试
npm run test:watch
```

### 3.3 持续集成配置

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - run: npm run test:e2e
```

## 4. 测试质量保证

### 4.1 覆盖率目标

| 测试类型 | 行覆盖率 | 分支覆盖率 | 函数覆盖率 | 语句覆盖率 |
|----------|----------|------------|------------|------------|
| 单元测试 | ≥90% | ≥85% | ≥90% | ≥90% |
| 集成测试 | ≥80% | ≥75% | ≥80% | ≥80% |
| 整体目标 | ≥85% | ≥80% | ≥85% | ≥85% |

### 4.2 测试数据管理

- **测试数据隔离**：每个测试用例使用独立的测试数据
- **数据清理**：测试结束后自动清理测试数据
- **数据工厂**：使用工厂模式生成测试数据

### 4.3 性能测试基准

```javascript
describe('性能基准测试', () => {
  test('游戏创建性能', async () => {
    const startTime = performance.now()
    await createGame()
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(100) // 100ms内完成
  })
})
```

## 5. 测试最佳实践

### 5.1 测试编写原则

- **AAA模式**：Arrange, Act, Assert
- **单一职责**：每个测试只验证一个功能点
- **独立性**：测试之间不相互依赖
- **可重复性**：测试结果可重复

### 5.2 命名规范

```javascript
describe('PlayerManager', () => {
  describe('addPlayer', () => {
    test('should add player successfully when valid player provided', () => {
      // 测试实现
    })
    
    test('should throw error when invalid player provided', () => {
      // 测试实现
    })
  })
})
```

### 5.3 错误测试策略

- **边界值测试**：测试边界条件
- **异常路径测试**：测试错误处理逻辑
- **负面测试**：测试非法输入的处理

## 总结

本测试设计报告建立了完整的测试体系，涵盖了从单元测试到端到端测试的全方位测试策略。通过分层测试、高覆盖率要求和持续集成，确保了代码质量和系统稳定性。

**关键特性：**
- ✅ **完整覆盖**：单元、集成、端到端三层测试
- ✅ **高质量标准**：85%+的覆盖率目标
- ✅ **自动化执行**：CI/CD集成和自动化测试
- ✅ **性能监控**：性能基准测试和监控
- ✅ **最佳实践**：遵循测试最佳实践和规范
