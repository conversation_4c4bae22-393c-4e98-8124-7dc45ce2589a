
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for model/core</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> model/core</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">9.61% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>37/385</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">5.48% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>13/237</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.77% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/90</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">9.97% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>37/371</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Constants.js"><a href="Constants.js.html">Constants.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	</tr>

<tr>
	<td class="file low" data-value="ErrorCodes.js"><a href="ErrorCodes.js.html">ErrorCodes.js</a></td>
	<td data-value="25" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="12" class="abs low">3/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="10" class="abs low">3/10</td>
	</tr>

<tr>
	<td class="file low" data-value="ErrorHandler.js"><a href="ErrorHandler.js.html">ErrorHandler.js</a></td>
	<td data-value="6.32" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.32" class="pct low">6.32%</td>
	<td data-value="79" class="abs low">5/79</td>
	<td data-value="4.68" class="pct low">4.68%</td>
	<td data-value="64" class="abs low">3/64</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="14" class="abs low">1/14</td>
	<td data-value="6.32" class="pct low">6.32%</td>
	<td data-value="79" class="abs low">5/79</td>
	</tr>

<tr>
	<td class="file low" data-value="Game.js"><a href="Game.js.html">Game.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="48" class="abs low">0/48</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="75" class="abs low">0/75</td>
	</tr>

<tr>
	<td class="file low" data-value="GameError.js"><a href="GameError.js.html">GameError.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="29" class="abs low">0/29</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	</tr>

<tr>
	<td class="file low" data-value="GameEventHandler.js"><a href="GameEventHandler.js.html">GameEventHandler.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="83" class="abs low">0/83</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="45" class="abs low">0/45</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	</tr>

<tr>
	<td class="file low" data-value="StateMachine.js"><a href="StateMachine.js.html">StateMachine.js</a></td>
	<td data-value="29.5" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.5" class="pct low">29.5%</td>
	<td data-value="61" class="abs low">18/61</td>
	<td data-value="32.25" class="pct low">32.25%</td>
	<td data-value="31" class="abs low">10/31</td>
	<td data-value="37.5" class="pct low">37.5%</td>
	<td data-value="16" class="abs low">6/16</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="60" class="abs low">18/60</td>
	</tr>

<tr>
	<td class="file low" data-value="VictoryChecker.js"><a href="VictoryChecker.js.html">VictoryChecker.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-16T07:21:32.625Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    