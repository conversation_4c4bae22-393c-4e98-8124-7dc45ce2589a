# 狼人杀插件开发最佳实践

**版本：** v0.6.0  
**制定日期：** 2025-01-16  
**适用范围：** 项目开发、维护和扩展  
**基于：** 项目实践经验和行业最佳实践

## 概述

本文档总结了狼人杀插件项目开发过程中积累的最佳实践，为团队成员提供统一的开发规范和质量标准。

## 1. 架构设计最佳实践

### 1.1 分层架构原则

**严格遵循四层架构：**
```
Apps层 (应用入口) → Services层 (服务组件) → Core层 (核心逻辑) → Foundation层 (基础设施)
```

**依赖关系规则：**
- ✅ 上层可以依赖下层
- ❌ 下层不能依赖上层
- ❌ 同层组件避免直接依赖

**示例：**
```javascript
// ✅ 正确：Apps层依赖Services层
import { GameRegistry } from '../model/services/GameRegistry.js'

// ❌ 错误：Foundation层依赖Core层
// import { Game } from '../core/Game.js' // 在Player.js中
```

### 1.2 事件驱动设计

**事件命名规范：**
```javascript
// 使用动词+名词的格式
this.emit('gameStart', { gameId: this.id })
this.emit('playerJoin', { player, gameId: this.id })
this.emit('stateChanged', { from: oldState, to: newState })
```

**事件处理最佳实践：**
```javascript
// ✅ 异步事件处理
this.on('playerAction', async (action) => {
  try {
    await this.handleAction(action)
  } catch (error) {
    this.emit('error', error)
  }
})

// ✅ 事件解耦
class GameEventHandler {
  constructor(game) {
    this.game = game
    this.setupEventListeners()
  }
  
  setupEventListeners() {
    this.game.on('stateChanged', this.handleStateChange.bind(this))
  }
}
```

### 1.3 状态管理

**状态转换验证：**
```javascript
// ✅ 严格的状态转换验证
const validationResult = isValidTransition(fromState, toState, this.game)
if (!validationResult.allowed) {
  throw new InvalidStateTransitionError(validationResult.reason)
}
```

**状态历史记录：**
```javascript
// ✅ 完整的状态历史
recordStateHistory(state) {
  const historyEntry = {
    stateType: state.constructor.name,
    timestamp: new Date(),
    turn: this.game.turn,
    context: this.getTransitionContext()
  }
  this.stateHistory.push(historyEntry)
}
```

## 2. 代码编写最佳实践

### 2.1 命名规范

**类名：** PascalCase
```javascript
class PlayerManager { }
class GameEventHandler { }
class WolfRole { }
```

**方法名：** camelCase，动词开头
```javascript
addPlayer(player) { }
removePlayer(playerId) { }
checkVictoryCondition() { }
```

**常量：** UPPER_SNAKE_CASE
```javascript
const GAME_STATES = {
  NIGHT: 'night',
  DAY: 'day'
}

const MAX_PLAYERS = 12
```

**私有方法：** 下划线前缀
```javascript
class PlayerManager {
  _validatePlayer(player) { }
  _updateCache() { }
}
```

### 2.2 错误处理

**自定义错误类：**
```javascript
class GameError extends Error {
  constructor(message, code = 'GAME_ERROR') {
    super(message)
    this.name = 'GameError'
    this.code = code
  }
}

class InvalidPlayerError extends GameError {
  constructor(message) {
    super(message, 'INVALID_PLAYER')
  }
}
```

**错误处理模式：**
```javascript
// ✅ 统一错误处理
async function executeAction(action) {
  try {
    const result = await this.processAction(action)
    return { success: true, result }
  } catch (error) {
    this.logger.error('Action execution failed', { action, error })
    this.emit('error', error)
    return { success: false, error: error.message }
  }
}
```

### 2.3 异步处理

**Promise使用：**
```javascript
// ✅ 正确的异步处理
async function initializeGame() {
  try {
    await this.loadConfiguration()
    await this.initializePlayers()
    await this.setupGameState()
  } catch (error) {
    throw new GameInitializationError(`Failed to initialize game: ${error.message}`)
  }
}

// ✅ 并行处理独立操作
async function preloadResources() {
  const [roles, configs, templates] = await Promise.all([
    this.loadRoles(),
    this.loadConfigs(),
    this.loadTemplates()
  ])
  return { roles, configs, templates }
}
```

## 3. 性能优化最佳实践

### 3.1 缓存策略

**多级缓存设计：**
```javascript
class PlayerManager {
  constructor() {
    this._cacheSystem = {
      alivePlayers: {
        cache: null,
        campExclusions: new Map(),
        roleTypes: new Map(),
        lastInvalidation: Date.now()
      }
    }
  }
  
  // ✅ 智能缓存失效
  invalidateCache(reason) {
    this._cacheSystem.alivePlayers.cache = null
    this._cacheSystem.alivePlayers.campExclusions.clear()
    this._cacheSystem.alivePlayers.lastInvalidation = Date.now()
  }
}
```

### 3.2 内存管理

**资源清理：**
```javascript
class Game extends EventEmitter {
  cleanup() {
    // ✅ 移除所有事件监听器
    this.removeAllListeners()
    
    // ✅ 清理管理器资源
    this.playerManager.cleanup()
    this.stateManager.cleanup()
    
    // ✅ 清理引用
    this.players = null
    this.currentState = null
  }
}
```

### 3.3 预加载优化

**模块预加载：**
```javascript
// ✅ 角色模块预加载
class RoleFactory {
  static async preloadRoleModules() {
    const loadPromises = [
      import('./WolfRole.js').then(module => 
        RoleFactory.roleModules.WolfRole = module.WolfRole),
      import('./ProphetRole.js').then(module => 
        RoleFactory.roleModules.ProphetRole = module.ProphetRole)
    ]
    await Promise.all(loadPromises)
  }
}
```

## 4. 测试最佳实践

### 4.1 测试结构

**AAA模式：**
```javascript
describe('PlayerManager', () => {
  describe('addPlayer', () => {
    test('should add valid player successfully', () => {
      // Arrange
      const playerManager = new PlayerManager()
      const player = createTestPlayer()
      
      // Act
      const result = playerManager.addPlayer(player)
      
      // Assert
      expect(result).toBe(true)
      expect(playerManager.getPlayerCount()).toBe(1)
    })
  })
})
```

### 4.2 模拟对象

**工厂模式创建Mock：**
```javascript
class MockFactory {
  static createGame(options = {}) {
    return new MockGame({
      players: options.players || [],
      config: options.config || this.defaultConfig(),
      ...options
    })
  }
  
  static createPlayer(options = {}) {
    return new MockPlayer({
      id: options.id || generateId(),
      name: options.name || 'TestPlayer',
      ...options
    })
  }
}
```

### 4.3 异步测试

**正确的异步测试：**
```javascript
// ✅ 使用async/await
test('should handle async game creation', async () => {
  const game = await createGame()
  expect(game.isInitialized).toBe(true)
})

// ✅ 测试Promise rejection
test('should reject invalid configuration', async () => {
  await expect(createGame({ invalidConfig: true }))
    .rejects
    .toThrow('Invalid configuration')
})
```

## 5. 文档编写最佳实践

### 5.1 代码注释

**类级别注释：**
```javascript
/**
 * 游戏核心协调器，管理游戏生命周期和状态
 * @class Game
 * @extends EventEmitter
 * @description 负责协调各个管理器，处理游戏逻辑，发出游戏事件
 */
class Game extends EventEmitter {
  /**
   * 添加玩家到游戏
   * @param {Player} player - 要添加的玩家实例
   * @returns {Promise<boolean>} 添加是否成功
   * @throws {InvalidPlayerError} 当玩家无效时抛出
   * @throws {MaxPlayersExceededError} 当超过最大玩家数时抛出
   */
  async addPlayer(player) {
    // 实现逻辑
  }
}
```

### 5.2 README文档

**结构化README：**
```markdown
# 项目名称

## 快速开始
## 安装说明
## 使用示例
## API文档
## 贡献指南
## 许可证
```

## 6. 版本控制最佳实践

### 6.1 提交信息规范

**提交信息格式：**
```
type(scope): description

[optional body]

[optional footer]
```

**示例：**
```
feat(player): add player validation logic
fix(state): resolve state transition race condition
docs(api): update API documentation
test(core): add unit tests for Game class
```

### 6.2 分支管理

**分支命名规范：**
- `feature/player-management` - 新功能开发
- `bugfix/state-transition-issue` - Bug修复
- `hotfix/critical-security-fix` - 紧急修复
- `refactor/code-optimization` - 代码重构

## 7. 代码审查最佳实践

### 7.1 审查清单

**功能性检查：**
- [ ] 代码实现符合需求
- [ ] 边界条件处理完善
- [ ] 错误处理机制健全

**质量检查：**
- [ ] 代码风格一致
- [ ] 命名规范清晰
- [ ] 注释完整准确

**性能检查：**
- [ ] 无明显性能问题
- [ ] 内存使用合理
- [ ] 算法效率优化

### 7.2 审查反馈

**建设性反馈：**
```
// ✅ 好的反馈
建议使用Map替代Object来提升查找性能，特别是在玩家数量较多的情况下。

// ❌ 不好的反馈
这里有问题。
```

## 总结

这些最佳实践基于项目实际开发经验总结，旨在提升代码质量、开发效率和团队协作。建议团队成员在开发过程中严格遵循这些规范。

**关键原则：**
- 🏗️ **架构清晰**：遵循分层架构和设计模式
- 📝 **代码规范**：统一的命名和编码风格
- 🧪 **测试驱动**：完善的测试覆盖和质量保证
- 📚 **文档完整**：清晰的代码注释和技术文档
- 🔄 **持续改进**：定期回顾和优化开发实践

建议定期回顾和更新这些最佳实践，确保与项目发展和技术演进保持同步。
